using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using VIC.Core;

namespace VIC.Road
{
    public struct TimeData
    {
        public int RedTime;
        public int YelTime;
        public int GreTime;
    }
    public class TimeChangePanel : MonoBehaviour
    {
        [Header("���")]
        public TMPro.TextMeshProUGUI[] texts;
        public InputField[] fields;
        public Button comfirmBtn;
        public Button closeBtn;
        private TimeData data;
        private void Awake()
        {
            fields[0].onValueChanged.AddListener((value) => {
                int.TryParse(value, out data.RedTime);});
            fields[1].onValueChanged.AddListener((value) => { int.TryParse(value, out data.YelTime); });
            fields[2].onValueChanged.AddListener((value) => { int.TryParse(value, out data.GreTime); });
            comfirmBtn.onClick.AddListener(OnBtnClick);
            closeBtn.onClick.AddListener(() => {
                EventCenter.Instance.EventTrigger(EventName.SetCurTrafficOutline, false);
                this.gameObject.SetActive(false); });
            EventCenter.Instance.AddEventListener<bool>(EventName.ShowTimeChangePanel, ShowPanel);
            EventCenter.Instance.AddEventListener<TimeData>(EventName.InitTimeData, InitTimeData);
            this.gameObject.SetActive(false);
        }
        private void OnBtnClick()
        {
            EventCenter.Instance.EventTrigger(EventName.SetPlayMakerAtionData, data);
            EventCenter.Instance.EventTrigger(EventName.SetCurTrafficOutline, false);
            this.gameObject.SetActive(false);
        }
        public void ShowPanel(bool isActive)
        {
            this.gameObject.SetActive(isActive);
        }
        public void InitTimeData(TimeData timeData)
        {
            // texts[0].text = $"{timeData.RedTime}";
            fields[0].text = timeData.RedTime.ToString();
            fields[1].text = timeData.YelTime.ToString();
            fields[2].text = timeData.GreTime.ToString();
        }
        private void Update()
        {

        }
    }

}

